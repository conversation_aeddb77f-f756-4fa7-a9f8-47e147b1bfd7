"use client";
import { Input } from "@heroui/input";
import { But<PERSON> } from "@heroui/react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>eader,
} from "@heroui/react";
import { useState } from "react";

interface Article {
	id: string;
	title: string;
	description: string;
	topic: string;
	published: string;
	status?: string;
	featured?: boolean;
	approved?: string;
	placement?: number;
}

interface ArticleManagementModalProps {
	isOpen: boolean;
	onClose: () => void;
	article: Article;
	onUpdate: () => void;
}

interface PendingChanges {
	approve?: boolean;
	completed?: boolean;
	featured?: boolean;
	placement?: number;
	publicationDate?: string;
}

export default function ArticleManagementModal({
	isOpen,
	onClose,
	article,
	onUpdate,
}: ArticleManagementModalProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [placement, setPlacement] = useState(
		article.placement?.toString() || "0",
	);
	const [publicationDate, setPublicationDate] = useState(
		article.published
			? new Date(article.published).toISOString().split("T")[0]
			: "",
	);
	const [isFeatured, setIsFeatured] = useState(article.featured || false);
	const [successMessage, setSuccessMessage] = useState("");
	const [errorMessage, setErrorMessage] = useState("");
	const [pendingChanges, setPendingChanges] = useState<PendingChanges>({});

	const handleApiCall = async (
		endpoint: string,
		method = "PUT",
		body?: any,
	) => {
		try {
			const response = await fetch(`/api/articles/${article.id}/${endpoint}`, {
				method,
				headers: {
					"Content-Type": "application/json",
				},
				body: body ? JSON.stringify(body) : undefined,
			});

			if (response.ok) {
				setSuccessMessage("Action completed successfully!");
				onUpdate();
				setTimeout(() => setSuccessMessage(""), 3000);
			} else {
				const data = await response.json();
				setErrorMessage(data.message || "Action failed");
				setTimeout(() => setErrorMessage(""), 3000);
			}
		} catch (error) {
			setErrorMessage("An error occurred");
			setTimeout(() => setErrorMessage(""), 3000);
			console.error("Error:", error);
		}
	};

	const handleApprove = () => {
		setPendingChanges((prev) => ({ ...prev, approve: true }));
	};

	const handleMarkComplete = () => {
		setPendingChanges((prev) => ({ ...prev, completed: true }));
	};

	const handleFeatureToggle = () => {
		const newFeaturedState = !isFeatured;
		setIsFeatured(newFeaturedState);
		setPendingChanges((prev) => ({ ...prev, featured: newFeaturedState }));
	};

	const validatePublicationDate = (date: string) => {
		if (!date) return false;
		const selectedDate = new Date(date);
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		return selectedDate >= today;
	};

	const handlePlacementChange = (value: string) => {
		setPlacement(value);
		const placementNumber = Number.parseInt(value, 10);
		if (!isNaN(placementNumber) && placementNumber >= 0) {
			setPendingChanges((prev) => ({ ...prev, placement: placementNumber }));
		}
	};

	const handlePublicationDateChange = (date: string) => {
		setPublicationDate(date);
		if (validatePublicationDate(date)) {
			setPendingChanges((prev) => ({ ...prev, publicationDate: date }));
			setErrorMessage("");
		} else {
			setErrorMessage("Publication date must be today or in the future");
			setTimeout(() => setErrorMessage(""), 3000);
		}
	};

	const handleUpdate = async () => {
		setIsLoading(true);

		try {
			const promises = [];

			if (pendingChanges.approve) {
				promises.push(handleApiCall("approve"));
			}

			if (pendingChanges.completed) {
				promises.push(handleApiCall("completed"));
			}

			if (pendingChanges.featured !== undefined) {
				promises.push(handleApiCall("feature"));
			}

			if (pendingChanges.placement !== undefined) {
				promises.push(
					handleApiCall("placement", "PUT", {
						placement: pendingChanges.placement,
					}),
				);
			}

			if (pendingChanges.publicationDate) {
				promises.push(
					handleApiCall("publication-date", "PUT", {
						publicationDate: pendingChanges.publicationDate,
					}),
				);
			}

			await Promise.all(promises);

			setPendingChanges({});
			setSuccessMessage("All changes applied successfully!");
			onUpdate();
			setTimeout(() => setSuccessMessage(""), 3000);
		} catch (error) {
			setErrorMessage("Failed to apply some changes");
			setTimeout(() => setErrorMessage(""), 3000);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Modal isOpen={isOpen} onClose={onClose} size="2xl">
			<ModalContent>
				<ModalHeader className="flex flex-col gap-1">
					<h3 className="text-lg font-semibold">Manage Article</h3>
					<p className="text-sm text-gray-600">{article.title}</p>
				</ModalHeader>
				<ModalBody>
					{successMessage && (
						<div className="p-3 mb-4 text-green-700 bg-green-100 rounded-md">
							{successMessage}
						</div>
					)}

					{errorMessage && (
						<div className="p-3 mb-4 text-red-700 bg-red-100 rounded-md">
							{errorMessage}
						</div>
					)}

					<div className="space-y-6">
						<div className="space-y-3">
							<h4 className="font-medium">Quick Actions</h4>
							<div className="flex flex-wrap gap-2">
								<Button
									color={pendingChanges.approve ? "success" : "default"}
									variant={pendingChanges.approve ? "solid" : "flat"}
									onPress={handleApprove}
									size="sm"
								>
									{pendingChanges.approve ? "✓ Will Approve" : "Approve"}
								</Button>
								<Button
									color={pendingChanges.completed ? "primary" : "default"}
									variant={pendingChanges.completed ? "solid" : "flat"}
									onPress={handleMarkComplete}
									size="sm"
								>
									{pendingChanges.completed
										? "✓ Will Complete"
										: "Mark Complete"}
								</Button>
								<Button
									color={isFeatured ? "warning" : "secondary"}
									variant={
										pendingChanges.featured !== undefined ? "solid" : "flat"
									}
									onPress={handleFeatureToggle}
									size="sm"
								>
									{pendingChanges.featured !== undefined
										? `✓ Will ${isFeatured ? "Feature" : "Unfeature"}`
										: isFeatured
											? "Unfeature"
											: "Feature"}
								</Button>
							</div>
							{Object.keys(pendingChanges).length > 0 && (
								<p className="text-xs font-medium text-blue-600">
									{Object.keys(pendingChanges).length} change(s) pending. Click
									"Update All Changes" to apply.
								</p>
							)}
						</div>

						<div className="space-y-2">
							<label className="text-sm font-medium">Placement</label>
							<Input
								type="number"
								value={placement}
								onChange={(e) => handlePlacementChange(e.target.value)}
								placeholder="Enter placement number"
								min="0"
								className="w-full"
							/>
							{pendingChanges.placement !== undefined && (
								<p className="text-xs text-blue-600">
									Pending: Will be set to {pendingChanges.placement}
								</p>
							)}
						</div>

						<div className="space-y-2">
							<label className="text-sm font-medium">Publication Date</label>
							<Input
								type="date"
								value={publicationDate}
								onChange={(e) => handlePublicationDateChange(e.target.value)}
								className="w-full"
								min={new Date().toISOString().split("T")[0]}
							/>
							{pendingChanges.publicationDate && (
								<p className="text-xs text-blue-600">
									Pending: Will be set to{" "}
									{new Date(
										pendingChanges.publicationDate,
									).toLocaleDateString()}
								</p>
							)}
						</div>
					</div>
				</ModalBody>
				<ModalFooter>
					<Button color="danger" variant="light" onPress={onClose}>
						Close
					</Button>
					<Button
						color="primary"
						onPress={handleUpdate}
						isLoading={isLoading}
						isDisabled={Object.keys(pendingChanges).length === 0}
					>
						{Object.keys(pendingChanges).length > 0
							? `Apply ${Object.keys(pendingChanges).length} Change(s)`
							: "No Changes to Apply"}
					</Button>
				</ModalFooter>
			</ModalContent>
		</Modal>
	);
}
