"use client";

import { <PERSON><PERSON> } from "@heroui/react";
import { Input } from "@heroui/input";
import { Switch } from "@heroui/react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "@heroui/react";
import { useState } from "react";

interface Article {
	id: string;
	title: string;
	description: string;
	topic: string;
	published: string;
	status?: string;
	featured?: boolean;
	approved?: string;
	placement?: number;
}

interface ArticleManagementModalProps {
	isOpen: boolean;
	onClose: () => void;
	article: Article;
	onUpdate: () => void;
}

export default function ArticleManagementModal({
	isOpen,
	onClose,
	article,
	onUpdate,
}: ArticleManagementModalProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [placement, setPlacement] = useState(article.placement?.toString() || "0");
	const [publicationDate, setPublicationDate] = useState(
		article.published ? new Date(article.published).toISOString().split('T')[0] : ""
	);
	const [isFeatured, setIsFeatured] = useState(article.featured || false);
	const [successMessage, setSuccessMessage] = useState("");
	const [errorMessage, setErrorMessage] = useState("");

	const handleApiCall = async (endpoint: string, method: string = "PUT", body?: any) => {
		try {
			const response = await fetch(`/api/articles/${article.id}/${endpoint}`, {
				method,
				headers: {
					"Content-Type": "application/json",
				},
				body: body ? JSON.stringify(body) : undefined,
			});

			if (response.ok) {
				setSuccessMessage("Action completed successfully!");
				onUpdate();
				setTimeout(() => setSuccessMessage(""), 3000);
			} else {
				const data = await response.json();
				setErrorMessage(data.message || "Action failed");
				setTimeout(() => setErrorMessage(""), 3000);
			}
		} catch (error) {
			setErrorMessage("An error occurred");
			setTimeout(() => setErrorMessage(""), 3000);
			console.error("Error:", error);
		}
	};

	const handleApprove = () => {
		handleApiCall("approve");
	};

	const handleMarkComplete = () => {
		handleApiCall("completed");
	};

	const handleFeatureToggle = () => {
		handleApiCall("feature");
		setIsFeatured(!isFeatured);
	};

	const handleSetPlacement = () => {
		const placementNumber = parseInt(placement, 10);
		if (isNaN(placementNumber) || placementNumber < 0) {
			setErrorMessage("Please enter a valid placement number");
			setTimeout(() => setErrorMessage(""), 3000);
			return;
		}
		handleApiCall("placement", "PUT", { placement: placementNumber });
	};

	const handleSetPublicationDate = () => {
		if (!publicationDate) {
			setErrorMessage("Please select a publication date");
			setTimeout(() => setErrorMessage(""), 3000);
			return;
		}
		handleApiCall("publication-date", "PUT", { publicationDate });
	};

	const handleUpdate = async () => {
		setIsLoading(true);

		// Execute all pending changes
		const promises = [];

		if (placement !== (article.placement?.toString() || "0")) {
			promises.push(handleSetPlacement());
		}

		if (publicationDate !== (article.published ? new Date(article.published).toISOString().split('T')[0] : "")) {
			promises.push(handleSetPublicationDate());
		}

		await Promise.all(promises);
		setIsLoading(false);
	};

	return (
		<Modal isOpen={isOpen} onClose={onClose} size="2xl">
			<ModalContent>
				<ModalHeader className="flex flex-col gap-1">
					<h3 className="text-lg font-semibold">Manage Article</h3>
					<p className="text-sm text-gray-600">{article.title}</p>
				</ModalHeader>
				<ModalBody>
					{successMessage && (
						<div className="p-3 mb-4 text-green-700 bg-green-100 rounded-md">
							{successMessage}
						</div>
					)}

					{errorMessage && (
						<div className="p-3 mb-4 text-red-700 bg-red-100 rounded-md">
							{errorMessage}
						</div>
					)}

					<div className="space-y-6">
						{/* Quick Actions */}
						<div className="space-y-3">
							<h4 className="font-medium">Quick Actions</h4>
							<div className="flex flex-wrap gap-2">
								<Button
									color="success"
									variant="flat"
									onPress={handleApprove}
									size="sm"
								>
									Approve
								</Button>
								<Button
									color="primary"
									variant="flat"
									onPress={handleMarkComplete}
									size="sm"
								>
									Mark Complete
								</Button>
								<Button
									color={isFeatured ? "warning" : "secondary"}
									variant="flat"
									onPress={handleFeatureToggle}
									size="sm"
								>
									{isFeatured ? "Unfeature" : "Feature"}
								</Button>
							</div>
						</div>

						{/* Placement */}
						<div className="space-y-2">
							<label className="text-sm font-medium">Placement</label>
							<div className="flex gap-2">
								<Input
									type="number"
									value={placement}
									onChange={(e) => setPlacement(e.target.value)}
									placeholder="Enter placement number"
									min="0"
									className="flex-1"
								/>
								<Button
									color="primary"
									variant="flat"
									onPress={handleSetPlacement}
									size="sm"
								>
									Set
								</Button>
							</div>
						</div>

						{/* Publication Date */}
						<div className="space-y-2">
							<label className="text-sm font-medium">Publication Date</label>
							<div className="flex gap-2">
								<Input
									type="date"
									value={publicationDate}
									onChange={(e) => setPublicationDate(e.target.value)}
									className="flex-1"
								/>
								<Button
									color="primary"
									variant="flat"
									onPress={handleSetPublicationDate}
									size="sm"
								>
									Set
								</Button>
							</div>
						</div>
					</div>
				</ModalBody>
				<ModalFooter>
					<Button color="danger" variant="light" onPress={onClose}>
						Close
					</Button>
					<Button
						color="primary"
						onPress={handleUpdate}
						isLoading={isLoading}
					>
						Update All Changes
					</Button>
				</ModalFooter>
			</ModalContent>
		</Modal>
	);
}
