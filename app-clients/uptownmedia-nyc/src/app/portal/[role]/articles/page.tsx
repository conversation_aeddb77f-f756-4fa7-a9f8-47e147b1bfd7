"use client";
import { Input } from "@heroui/input";
import { Button } from "@heroui/react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { use } from "react";

import type { GroupResponse } from "@/app/api/groups/dto/groupResponse";
import ArticleManagementModal from "@/components/ui/modal/ArticleManagementModal";
import { getGroupMembershipsByEditorAndGroupType } from "@/services/groupServices";

export default function ArticlesPage({
	params,
}: {
	params: Promise<{ role: "admin" | "author" | "editor" | "guest" }>;
}) {
	const { role } = use(params);
	const { data: session, status } = useSession();
	const router = useRouter();
	const [articles, setArticles] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [userGroup, setUserGroup] = useState<GroupResponse | null>(null);
	const [groupCodeCopied, setGroupCodeCopied] = useState(false);
	const [selectedArticle, setSelectedArticle] = useState<any>(null);
	const [isModalOpen, setIsModalOpen] = useState(false);

	useEffect(() => {
		const fetchData = async () => {
			try {
				let articlesEndpoint = "/api/articles";
				if (role === "editor") {
					articlesEndpoint = "/api/articles/editor";
				} else if (role === "author") {
					articlesEndpoint = "/api/articles/author";
				}

				const articlesResponse = await fetch(articlesEndpoint);
				if (articlesResponse.ok) {
					const articlesData = await articlesResponse.json();
					setArticles(articlesData.articles || []);
				}

				if (role === "editor" && session?.user?.id) {
					try {
						const groups = await getGroupMembershipsByEditorAndGroupType(
							session.user.id,
							"",
						);
						if (groups && groups.length > 0) {
							setUserGroup(groups[0]);
						}
					} catch (error) {
						console.error("Error fetching group data:", error);
					}
				}
			} catch (error) {
				console.error("Error fetching data:", error);
			} finally {
				setIsLoading(false);
			}
		};

		if (session?.user?.id) {
			fetchData();
		}
	}, [role, session?.user?.id]);

	const handleCopyGroupCode = async () => {
		if (userGroup?.sharedPasscode) {
			try {
				await navigator.clipboard.writeText(userGroup.sharedPasscode);
				setGroupCodeCopied(true);
				setTimeout(() => setGroupCodeCopied(false), 2000);
			} catch (error) {
				console.error("Failed to copy group code:", error);
			}
		}
	};

	const handleOpenModal = (article: any) => {
		setSelectedArticle(article);
		setIsModalOpen(true);
	};

	const handleCloseModal = () => {
		setIsModalOpen(false);
		setSelectedArticle(null);
	};

	const handleArticleUpdate = () => {
		// Refresh articles list
		if (session?.user?.id) {
			const fetchData = async () => {
				try {
					let articlesEndpoint = "/api/articles";
					if (role === "editor") {
						articlesEndpoint = "/api/articles/editor";
					} else if (role === "author") {
						articlesEndpoint = "/api/articles/author";
					}

					const articlesResponse = await fetch(articlesEndpoint);
					if (articlesResponse.ok) {
						const articlesData = await articlesResponse.json();
						setArticles(articlesData.articles || []);
					}
				} catch (error) {
					console.error("Error fetching data:", error);
				}
			};
			fetchData();
		}
	};

	if (status === "loading") {
		return <div className="p-8 text-center">Loading...</div>;
	}

	if (status === "unauthenticated") {
		router.push("/auth/sign-in");

		return null;
	}

	return (
		<div className="max-w-4xl p-6 mx-auto">
			<div className="flex items-center justify-between mb-8">
				<h1 className="text-2xl font-bold">Articles</h1>
				{(role === "author" || role === "admin") && (
					<Link href={`/portal/${role}/articles/create`}>
						<Button color="primary">Create New Article</Button>
					</Link>
				)}
			</div>

			{role === "editor" && userGroup?.sharedPasscode && (
				<div className="p-4 mb-6 border border-blue-200 rounded-lg bg-blue-50">
					<div className="flex items-center justify-between">
						<div>
							<h3 className="text-sm font-medium text-blue-800">
								Your Group Code
							</h3>
							<p className="mt-1 text-xs text-blue-600">
								Share this code with authors to invite them to your group
							</p>
						</div>
						<div className="flex items-center gap-2">
							<Input
								readOnly
								className="w-40 font-mono text-sm"
								value={userGroup.sharedPasscode}
								variant="bordered"
								size="sm"
							/>
							<Button
								size="sm"
								variant="flat"
								color="primary"
								onPress={handleCopyGroupCode}
							>
								{groupCodeCopied ? "Copied!" : "Copy"}
							</Button>
						</div>
					</div>
				</div>
			)}

			{isLoading ? (
				<div className="p-4 text-center">Loading articles...</div>
			) : articles.length > 0 ? (
				<div className="space-y-4">
					{articles.map(
						(article: {
							id: string;
							title: string;
							description: string;
							topic: string;
							published: string;
							status?: string;
							featured?: boolean;
							approved?: string;
							placement?: number;
						}) => (
							<div
								key={article.id}
								className={`p-4 transition-shadow border rounded-md shadow-sm hover:shadow-md ${
									article.status === "saved" && role === "author"
										? "cursor-pointer hover:bg-gray-50"
										: ""
								}`}
								onClick={() => {
									if (article.status === "saved" && role === "author") {
										router.push(
											`/portal/author/articles/create?edit=${article.id}`,
										);
									}
								}}
							>
								<div className="flex items-start justify-between mb-2">
									<h2 className="text-xl font-semibold">{article.title}</h2>
									<div className="flex items-center gap-2">
										{article.status === "saved" && (
											<>
												<span className="px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 rounded-full">
													Draft
												</span>
												{role === "author" && (
													<span className="text-xs text-gray-500">
														Click to edit
													</span>
												)}
											</>
										)}
										{article.status === "submitted" && (
											<span className="px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full">
												Submitted
											</span>
										)}
										{article.status === "completed" && (
											<span className="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">
												Completed
											</span>
										)}
										{article.featured && (
											<span className="px-2 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded-full ml-2">
												Featured
											</span>
										)}
									</div>
								</div>
								<p className="text-gray-600 line-clamp-2">
									{article.description}
								</p>
								<div className="flex items-center justify-between mt-2">
									<span className="text-sm text-gray-500">
										Topic: {article.topic}
									</span>
									<div className="flex items-center gap-2">
										<span className="text-sm text-gray-500">
											Published:{" "}
											{new Date(article.published).toLocaleDateString()}
										</span>
										{role === "editor" && (
											<Button
												size="sm"
												color="primary"
												variant="flat"
												onPress={() => handleOpenModal(article)}
											>
												Manage
											</Button>
										)}
									</div>
								</div>
							</div>
						),
					)}
				</div>
			) : (
				<div className="p-8 text-center text-gray-500">
					No articles found. {role === "author" && "Create your first article!"}
					{role === "editor" &&
						"Articles submitted by authors in your group will appear here."}
					{role === "admin" && "Create your first article!"}
				</div>
			)}

			{/* Article Management Modal for Editors */}
			{selectedArticle && (
				<ArticleManagementModal
					isOpen={isModalOpen}
					onClose={handleCloseModal}
					article={selectedArticle}
					onUpdate={handleArticleUpdate}
				/>
			)}
		</div>
	);
}
