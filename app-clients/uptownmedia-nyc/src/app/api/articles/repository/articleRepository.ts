import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
	UpdateCommand,
	type UpdateCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { BaseDynamoDBRepository } from "../../shared/dynamoDbRepository";
import { Article } from "../dao/article";

import { configDbEnv } from "@/utils/configDbEnv";

/**
 * Repository for interacting with the Articles table in DynamoDB
 */
export class ArticleDynamoDBRepository extends BaseDynamoDBRepository {
	/**
	 * Constructor to set the tableName for ArticleDynamoDBRepository
	 */
	constructor() {
		super(configDbEnv.articleTable); // Sets the tableName for this repository
	}

	/**
	 * Convert a DynamoDB object to an instance of Article
	 * @param item Object received from DynamoDB
	 * @returns Instance of Article
	 */

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToArticle(item: any): Article {
		const article = new Article(
			item.title,
			item.description,
			item.author,
			item.topic,
			item.published,
			item.image,
			item.featured,
			item.approved,
			item.approvedBy,
			item.placement,
			item.userId,
			item.status,
		);

		// Manually set the id and created values as they are not set by the constructor
		Object.defineProperty(article, "id", {
			value: item.id,
			writable: false,
			configurable: true,
		});

		Object.defineProperty(article, "created", {
			value: item.created,
			writable: false,
			configurable: true,
		});

		return article;
	}

	/**
	 * Convert an Article instance to an object for saving in DynamoDB
	 * @param article Instance of Article
	 * @returns Object ready to be saved in DynamoDB
	 */

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToDynamoDBItem(article: Article): Record<string, any> {
		return {
			id: article.getId(),
			created: article.getCreated(),
			title: article.getTitle(),
			description: article.getDescription(),
			author: article.getAuthor(),
			topic: article.getTopic(),
			published: article.getPublished(),
			image: article.getImage(),
			featured: article.isFeatured(),
			approved: article.getApproved(),
			approvedBy: article.getApprovedBy(),
			placement: article.getPlacement(),
			userId: article.getUserId(),
			status: article.getStatus(),
		};
	}

	/**
	 * Create or update an article in DynamoDB
	 * @param article Article instance to save
	 * @returns Promise with the result of the operation
	 */
	async saveArticle(article: Article): Promise<void> {
		try {
			const item = this.mapToDynamoDBItem(article);

			const params: PutCommandInput = {
				TableName: this.tableName,
				Item: item,
			};

			const command = new PutCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error saving article with id ${article.getId()}:`, error);
			throw error;
		}
	}

	/**
	 * Get an article by its ID
	 * @param articleId Article ID
	 * @returns Promise with the found article or null
	 */
	async getArticleById(articleId: string): Promise<Article | null> {
		try {
			const item = await this.getItemById([["id", articleId]]);

			if (!item) {
				return null;
			}

			return this.mapToArticle(item);
		} catch (error) {
			console.error(`Error fetching article with id ${articleId}:`, error);
			throw error;
		}
	}

	/**
	 * Get all articles
	 * @returns Promise with a list of all articles
	 */
	async getAllArticles(): Promise<Article[]> {
		try {
			const items = await this.getAllItems();

			return items.map((item) => this.mapToArticle(item));
		} catch (error) {
			console.error("Error fetching all articles:", error);
			throw error;
		}
	}

	/**
	 * Delete an article by its ID
	 * @param articleId ID of the article to delete
	 * @returns Promise with the result of the operation
	 */
	async deleteArticle(articleId: string): Promise<void> {
		try {
			const params: DeleteCommandInput = {
				TableName: this.tableName,
				Key: {
					id: articleId,
				},
			};

			const command = new DeleteCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error deleting article with id ${articleId}:`, error);
			throw error;
		}
	}

	/**
	 * Get articles by author
	 * @param authorId ID of the author to filter by
	 * @returns Promise with a list of filtered articles
	 */
	async getArticlesByAuthor(authorId: string): Promise<Article[]> {
		try {
			const items = await this.scanWithFilter("author = :authorId", {
				":authorId": authorId,
			});

			return items.map((item) => this.mapToArticle(item));
		} catch (error) {
			console.error(`Error fetching articles by author ${authorId}:`, error);
			throw error;
		}
	}

	/**
	 * Get articles by topic
	 * @param topic Topic to filter by
	 * @returns Promise with a list of filtered articles
	 */
	async getArticlesByTopic(topic: string): Promise<Article[]> {
		try {
			const items = await this.scanWithFilter("topic = :topic", {
				":topic": topic,
			});

			return items.map((item) => this.mapToArticle(item));
		} catch (error) {
			console.error(`Error fetching articles by topic ${topic}:`, error);
			throw error;
		}
	}

	/**
	 * Get featured articles
	 * @returns Promise with a list of featured articles
	 */
	async getFeaturedArticles(): Promise<Article[]> {
		try {
			const items = await this.scanWithFilter("featured = :featured", {
				":featured": true,
			});

			return items.map((item) => this.mapToArticle(item));
		} catch (error) {
			console.error("Error fetching featured articles:", error);
			throw error;
		}
	}

	/**
	 * Get articles by approval status
	 * @param approved Approval status ("approved", "pending", "rejected", etc.)
	 * @returns Promise with a list of articles filtered by approval status
	 */
	async getArticlesByApprovalStatus(approved: string): Promise<Article[]> {
		try {
			const items = await this.scanWithFilter("approved = :approved", {
				":approved": approved,
			});

			return items.map((item) => this.mapToArticle(item));
		} catch (error) {
			console.error(
				`Error fetching articles by approval status ${approved}:`,
				error,
			);
			throw error;
		}
	}

	/**
	 * Update the approval status of an article
	 * @param articleId ID of the article
	 * @param approved New approval status
	 * @param approvedBy ID of the user who approved
	 * @returns Promise with the result of the operation
	 */
	async updateApprovalStatus(
		articleId: string,
		approved: string,
		approvedBy: string,
	): Promise<void> {
		try {
			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: {
					id: articleId,
				},
				UpdateExpression: "set approved = :approved, approvedBy = :approvedBy",
				ExpressionAttributeValues: {
					":approved": approved,
					":approvedBy": approvedBy,
				},
				ReturnValues: "NONE",
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(
				`Error updating approval status for article ${articleId}:`,
				error,
			);
			throw error;
		}
	}

	/**
	 * Update the featured status of an article
	 * @param articleId ID of the article
	 * @param featured New featured status
	 * @returns Promise with the result of the operation
	 */
	async updateFeaturedStatus(
		articleId: string,
		featured: boolean,
	): Promise<void> {
		try {
			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: {
					id: articleId,
				},
				UpdateExpression: "set featured = :featured",
				ExpressionAttributeValues: {
					":featured": featured,
				},
				ReturnValues: "NONE",
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(
				`Error updating featured status for article ${articleId}:`,
				error,
			);
			throw error;
		}
	}
	public async getArticlesByUserId(userId: string): Promise<Article[]> {
		try {
			const items = await this.scanWithFilter("userId = :userId", {
				":userId": userId,
			});

			return items.map((item) => this.mapToArticle(item));
		} catch (error) {
			console.error(`Error fetching articles by userId ${userId}:`, error);
			throw error;
		}
	}

	/**
	 * Update the placement (display order) of an article
	 * @param articleId ID of the article
	 * @param placement New placement value
	 * @returns Promise with the result of the operation
	 */
	async updatePlacement(articleId: string, placement: number): Promise<void> {
		try {
			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: {
					id: articleId,
				},
				UpdateExpression: "set placement = :placement",
				ExpressionAttributeValues: {
					":placement": placement,
				},
				ReturnValues: "NONE",
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(
				`Error updating placement for article ${articleId}:`,
				error,
			);
			throw error;
		}
	}

	/**
	 * Update the main information of an article
	 * @param articleId ID of the article
	 * @param articleData Article data to update
	 * @returns Promise with the result of the operation
	 */
	async updateArticleInfo(
		articleId: string,
		articleData: {
			title?: string;
			description?: string;
			topic?: string;
			image?: string;
			published?: string;
		},
	): Promise<void> {
		try {
			// Dynamically build the update expression based on provided fields
			let updateExpression = "set";
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			const expressionAttributeValues: Record<string, any> = {};

			// Add fields to the update expression if present
			if (articleData.title) {
				updateExpression += " title = :title,";
				expressionAttributeValues[":title"] = articleData.title;
			}

			if (articleData.description) {
				updateExpression += " description = :description,";
				expressionAttributeValues[":description"] = articleData.description;
			}

			if (articleData.topic) {
				updateExpression += " topic = :topic,";
				expressionAttributeValues[":topic"] = articleData.topic;
			}

			if (articleData.image) {
				updateExpression += " image = :image,";
				expressionAttributeValues[":image"] = articleData.image;
			}

			if (articleData.published) {
				updateExpression += " published = :published,";
				expressionAttributeValues[":published"] = articleData.published;
			}

			// Remove trailing comma if present
			updateExpression = updateExpression.endsWith(",")
				? updateExpression.slice(0, -1)
				: updateExpression;

			// If there's nothing to update, return early
			if (Object.keys(expressionAttributeValues).length === 0) {
				return;
			}

			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: {
					id: articleId,
				},
				UpdateExpression: updateExpression,
				ExpressionAttributeValues: expressionAttributeValues,
				ReturnValues: "NONE",
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error updating info for article ${articleId}:`, error);
			throw error;
		}
	}

	/**
	 * Search articles by title or description
	 * @param searchTerm Search term
	 * @returns Promise with the list of articles matching the search
	 */
	async searchArticles(searchTerm: string): Promise<Article[]> {
		try {
			// Retrieve all articles to filter in memory
			// (DynamoDB doesn't support full-text search directly)
			const items = await this.getAllItems();

			// Filter articles containing the search term in title or description
			const filteredItems = items.filter((item) => {
				const term = searchTerm.toLowerCase();

				return (
					item.title.toLowerCase().includes(term) ||
					item.description.toLowerCase().includes(term)
				);
			});

			return filteredItems.map((item) => this.mapToArticle(item));
		} catch (error) {
			console.error(`Error searching articles with term ${searchTerm}:`, error);
			throw error;
		}
	}
}
