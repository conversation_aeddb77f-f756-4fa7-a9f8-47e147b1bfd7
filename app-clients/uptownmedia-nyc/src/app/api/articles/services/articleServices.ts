import type { ArticleRequest } from "../dto/articleRequest";

import { GroupMembershipServices } from "../../group-memberships/services/groupMembershipsServices";
import { GroupServices } from "../../groups/services/groupsServices";
import { NotFoundError } from "../../shared/errors";
import { Article, ArticleStatus } from "../dao/article";
import { ArticleDynamoDBRepository } from "../repository/articleRepository";

export class ArticleService {
	private repository: ArticleDynamoDBRepository;
	private groupServices: GroupServices;
	private memberServices: GroupMembershipServices;

	constructor() {
		this.repository = new ArticleDynamoDBRepository();
		this.groupServices = new GroupServices();
		this.memberServices = new GroupMembershipServices();
	}

	public async createArticle(request: ArticleRequest, userId: string) {
		const { title, description, author, topic, published, image } = request;

		const article = new Article(
			title,
			description,
			author,
			topic,
			published,
			image,
			false, // Default to false for new articles
			"", // Default to false for new articles
			"",
			0,
			userId,
			ArticleStatus.SUBMITTED,
		);

		await this.repository.saveArticle(article);
	}
	public async saveArticle(request: ArticleRequest, userId: string) {
		const { title, description, author, topic, published, image } = request;

		const article = new Article(
			title,
			description,
			author,
			topic,
			published,
			image,
			false, // Default to false for new articles
			"", // Default to false for new articles
			"",
			0,
			userId,
			ArticleStatus.SAVED,
		);

		await this.repository.saveArticle(article);
	}

	public async getAllArticles() {
		const listOfArticles = await this.repository.getAllArticles();

		return listOfArticles;
	}

	public async getArticleById(id: string) {
		const article = await this.repository.getArticleById(id);

		if (!article) {
			throw new NotFoundError(`Article with ID ${id} not found`);
		}

		return article;
	}
	public async getArticlesByUserId(userId: string) {
		const articles = await this.repository.getArticlesByUserId(userId);

		return articles;
	}
	public async approveArticle(articleId: string, userId: string) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		const date = new Date();

		article.setApproved(date.toISOString());
		article.setApprovedBy(userId);
		console.log("approving article", article);
		await this.repository.saveArticle(article);
	}
	public async isFeature(articleId: string) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		article.setFeatured(true);
		await this.repository.saveArticle(article);
	}

	public async setPlacement(articleId: string, placement: number) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		article.setPlacement(placement);
		await this.repository.saveArticle(article);

		return article;
	}
	public async changeArticleStatus(status: ArticleStatus, articleId: string) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		article.setStatus(status);
		await this.repository.saveArticle(article);
	}

	public async getArticlesByEditorGroups(editorId: string) {
		try {
			const group = await this.groupServices.getGroupByEditorId(editorId);
			const members = await this.memberServices.getGroupMembershipsByGroupId(
				group.getGroupId(),
			);

			const articleListPromises = members.map(
				async (member) => await this.getArticlesByUserId(member.getUserId()),
			);

			const articlesArrays = await Promise.all(articleListPromises);

			const articles = articlesArrays
				.flat()
				.filter((article) => article.getStatus() !== ArticleStatus.SAVED);

			return articles;
		} catch (error) {
			console.error("Error fetching articles for editor groups:", error);
			throw error;
		}
	}
	public async setPublicationDate(date: string, articleId: string) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		article.setPublished(date);
		await this.repository.saveArticle(article);

		return article;
	}
}
